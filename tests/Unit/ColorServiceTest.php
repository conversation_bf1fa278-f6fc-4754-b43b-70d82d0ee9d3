<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Services\ColorService;
use PHPUnit\Framework\TestCase;

class ColorServiceTest extends TestCase
{
    public function test_hex_to_hsl_conversion()
    {
        $hsl = ColorService::hexToHsl('#ff0000');

        $this->assertEquals(0, $hsl['h']);
        $this->assertEquals(100, $hsl['s']);
        $this->assertEquals(50, $hsl['l']);
    }

    public function test_hex_to_hsl_string()
    {
        $hslString = ColorService::hexToHslString('#00ff00');

        $this->assertEquals('120 100% 50%', $hslString);
    }

    public function test_get_contrast_color_for_light_background()
    {
        $contrast = ColorService::getContrastColor('#ffffff');

        $this->assertEquals('#000000', $contrast);
    }

    public function test_get_contrast_color_for_dark_background()
    {
        $contrast = ColorService::getContrastColor('#000000');

        $this->assertEquals('#ffffff', $contrast);
    }

    public function test_generate_primary_color_variables()
    {
        $variables = ColorService::generatePrimaryColorVariables('#ff0000');

        $this->assertArrayHasKey('primary_hsl', $variables);
        $this->assertArrayHasKey('primary_foreground_hsl', $variables);
        $this->assertEquals('0 100% 50%', $variables['primary_hsl']);
        $this->assertEquals('0 0% 0%', $variables['primary_foreground_hsl']);
    }

    public function test_generate_primary_color_variables_with_custom_text_color()
    {
        $variables = ColorService::generatePrimaryColorVariables('#ff0000', '#ffffff');

        $this->assertArrayHasKey('primary_hsl', $variables);
        $this->assertArrayHasKey('primary_foreground_hsl', $variables);
        $this->assertEquals('0 100% 50%', $variables['primary_hsl']);
        $this->assertEquals('0 0% 100%', $variables['primary_foreground_hsl']);
    }

    public function test_generate_primary_color_variables_with_null_text_color()
    {
        $variables = ColorService::generatePrimaryColorVariables('#ff0000', null);

        $this->assertArrayHasKey('primary_hsl', $variables);
        $this->assertArrayHasKey('primary_foreground_hsl', $variables);
        $this->assertEquals('0 100% 50%', $variables['primary_hsl']);
        $this->assertEquals('0 0% 0%', $variables['primary_foreground_hsl']);
    }
}
