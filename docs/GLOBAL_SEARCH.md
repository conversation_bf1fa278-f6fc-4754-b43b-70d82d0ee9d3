# Global Search Feature Documentation

## Overview

The Global Search feature provides a comprehensive search functionality across the Laravel application, allowing users to search for accounts, games, posts, categories, and navigation items from a single interface.

## Architecture

### Design Patterns Used

1. **Strategy Pattern**: Different search providers (Database, Custom) implement the `SearchProvider` interface
2. **Repository Pattern**: Database search is abstracted through the `DatabaseSearchProvider`
3. **Factory Pattern**: Search results are created using the `SearchResult` DTO
4. **Service Layer**: `SearchService` orchestrates multiple search providers

### Backend Components

#### Core Interfaces
- `App\Contracts\Searchable` - Interface for searchable models
- `App\Contracts\SearchProvider` - Interface for search providers

#### Services
- `App\Services\Search\SearchService` - Main search orchestrator
- `App\Services\Search\Providers\DatabaseSearchProvider` - Database search implementation
- `App\Services\Search\Providers\CustomSearchProvider` - Custom navigation items search

#### Data Transfer Objects
- `App\DataTransferObjects\SearchResult` - Standardized search result format

#### Traits
- `App\Concerns\Searchable` - Trait for searchable models with Vietnamese diacritic support

### Frontend Components

#### Vue.js Components
- `GlobalSearch.vue` - Main search component with modal interface
- `SearchInput.vue` - Search input with clear functionality
- `SearchResults.vue` - Results display with categorization
- `SearchResultItem.vue` - Individual result item with highlighting

## Features

### Backend Features

1. **Multi-Provider Search**: Combines database and custom search results
2. **Vietnamese Diacritic Support**: Normalizes Vietnamese text for better search matching
3. **Weighted Results**: Results are sorted by relevance/weight
4. **Flexible Model Integration**: Easy to add new searchable models
5. **Custom Navigation Items**: Predefined search results for common actions

### Frontend Features

1. **Real-time Search**: Debounced search as user types (300ms delay)
2. **Keyboard Navigation**: Arrow keys, Enter, and Escape support
3. **Global Shortcut**: Ctrl/Cmd + K to open search
4. **Responsive Design**: Works on desktop and mobile
5. **Search Highlighting**: Query terms are highlighted in results
6. **Categorized Results**: Results grouped by type (Account, Game, Post, etc.)
7. **Loading States**: Visual feedback during search
8. **Empty States**: Helpful messages when no results found

## API Endpoints

### Search API
```
GET /api/search?query={term}&limit={number}
```

**Parameters:**
- `query` (required): Search term (1-255 characters)
- `limit` (optional): Maximum results to return (1-50, default: 20)

**Response:**
```json
{
  "success": true,
  "data": {
    "query": "search term",
    "results": [
      {
        "id": "unique_id",
        "title": "Result Title",
        "description": "Result description",
        "type": "account|game|post|category|navigation",
        "url": "result_url",
        "image": "image_url",
        "category": "Category Name",
        "weight": 90,
        "metadata": {}
      }
    ],
    "total": 1
  }
}
```

## Searchable Models

### Currently Implemented
1. **Account** - Searches: name, description, content
2. **Game** - Searches: name, description
3. **Post** - Searches: title, description, content
4. **AccountCategory** - Searches: name, description

### Custom Search Items
- Login page
- Register page
- Deposit page
- Profile page
- Transaction history
- Purchased accounts

## Usage

### Adding New Searchable Models

1. Implement the `Searchable` interface:
```php
use App\Contracts\Searchable;
use App\Concerns\Searchable as SearchableTrait;

class YourModel extends Model implements Searchable
{
    use SearchableTrait;
    
    public function getSearchableColumns(): array
    {
        return ['column1', 'column2'];
    }
    
    public function toSearchResult(): array
    {
        return [
            'id' => 'model_' . $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'type' => 'model_type',
            'url' => route('model.show', $this),
            'image' => $this->image_url,
            'category' => 'Category Name',
            'weight' => 50,
            'metadata' => []
        ];
    }
}
```

2. Register in `SearchServiceProvider`:
```php
$databaseProvider->addSearchableModel(YourModel::class);
```

### Adding Custom Search Items

```php
$customProvider = app(CustomSearchProvider::class);
$customProvider->addCustomItem(new SearchResult(
    id: 'unique_id',
    title: 'Item Title',
    description: 'Item description',
    type: 'navigation',
    url: route('your.route'),
    category: 'Category',
    weight: 80
));
```

## Testing

Comprehensive test suite covers:
- API validation
- Database search functionality
- Custom search items
- Vietnamese diacritic handling
- Result sorting by weight
- Error handling

Run tests:
```bash
php artisan test tests/Feature/SearchTest.php
```

## Performance Considerations

1. **Debounced Requests**: Frontend debounces requests to reduce server load
2. **Limited Results**: Maximum 50 results per request
3. **Indexed Columns**: Ensure searchable columns are properly indexed
4. **Caching**: Consider implementing result caching for popular queries

## Browser Support

- Modern browsers with ES6+ support
- Vue.js 3 compatible browsers
- Keyboard navigation support

## Accessibility

- ARIA labels for screen readers
- Keyboard navigation support
- Focus management
- Semantic HTML structure
