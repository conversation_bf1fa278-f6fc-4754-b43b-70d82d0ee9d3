<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

class SearchResult
{
    public function __construct(
        public readonly string $id,
        public readonly string $title,
        public readonly string $description,
        public readonly string $type,
        public readonly string $url,
        public readonly ?string $image = null,
        public readonly ?string $category = null,
        public readonly int $weight = 0,
        public readonly array $metadata = []
    ) {}

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'type' => $this->type,
            'url' => $this->url,
            'image' => $this->image,
            'category' => $this->category,
            'weight' => $this->weight,
            'metadata' => $this->metadata,
        ];
    }
}
