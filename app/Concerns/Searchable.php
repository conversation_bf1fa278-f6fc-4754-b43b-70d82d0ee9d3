<?php

declare(strict_types=1);

namespace App\Concerns;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

trait Searchable
{
    public function scopeSearch(Builder $query, string $term): Builder
    {
        $searchableColumns = $this->getSearchableColumns();
        
        if (empty($searchableColumns)) {
            return $query;
        }

        $term = trim($term);
        if (empty($term)) {
            return $query;
        }

        return $query->where(function (Builder $query) use ($term, $searchableColumns) {
            foreach ($searchableColumns as $column) {
                if (Str::contains($column, '.')) {
                    $parts = explode('.', $column);
                    $relation = $parts[0];
                    $relationColumn = $parts[1];
                    
                    $query->orWhereHas($relation, function (Builder $subQuery) use ($relationColumn, $term) {
                        $subQuery->where($relationColumn, 'LIKE', "%{$term}%");
                    });
                } else {
                    $query->orWhere($column, 'LIKE', "%{$term}%");
                }
            }
        });
    }

    public function getSearchWeight(): int
    {
        return 50;
    }
}
