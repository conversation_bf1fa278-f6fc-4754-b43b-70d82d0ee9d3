<?php

declare(strict_types=1);

namespace App\Models;

use App\Concerns\Searchable as SearchableTrait;
use App\Contracts\Searchable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Game extends Model implements Searchable
{
    use HasFactory;
    use SoftDeletes;
    use SearchableTrait;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'is_visible' => 'bool',
        ];
    }

    public function publisher(): BelongsTo
    {
        return $this->belongsTo(Publisher::class);
    }

    public function categories(): HasMany
    {
        return $this->hasMany(AccountCategory::class);
    }

    /**
     * Get the searchable columns for the model.
     */
    public function getSearchableColumns(): array
    {
        return [
            'name',
            'description',
        ];
    }

    /**
     * Get the search result data for the model.
     */
    public function toSearchResult(): array
    {
        return [
            'id' => 'game_' . $this->id,
            'title' => $this->name,
            'description' => $this->description ?? 'Game ' . $this->name,
            'type' => 'game',
            'url' => route('games.show', $this->slug),
            'image' => $this->image,
            'category' => 'Game',
            'weight' => $this->is_visible ? 80 : 40,
            'metadata' => [
                'publisher' => $this->publisher->name ?? null,
                'is_visible' => $this->is_visible,
            ],
        ];
    }

    /**
     * Get the search weight/priority for ranking results.
     */
    public function getSearchWeight(): int
    {
        return $this->is_visible ? 80 : 40;
    }
}
