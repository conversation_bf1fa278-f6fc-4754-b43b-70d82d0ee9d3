<?php

declare(strict_types=1);

namespace App\Models;

use App\Concerns\Searchable as SearchableTrait;
use App\Contracts\Searchable;
use App\Enums\PostStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Post extends Model implements Searchable
{
    use SoftDeletes;
    use SearchableTrait;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'status' => PostStatus::class,
            'views' => 'int',
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the searchable columns for the model.
     */
    public function getSearchableColumns(): array
    {
        return [
            'title',
            'description',
            'content',
        ];
    }

    /**
     * Get the search result data for the model.
     */
    public function toSearchResult(): array
    {
        return [
            'id' => 'post_' . $this->id,
            'title' => $this->title,
            'description' => $this->description ?? 'Bài viết về ' . ($this->category->name ?? 'tin tức'),
            'type' => 'post',
            'url' => route('blog.show', $this->slug),
            'image' => $this->image,
            'category' => 'Tin tức',
            'weight' => $this->status === PostStatus::Published ? 70 : 30,
            'metadata' => [
                'status' => $this->status->value,
                'views' => $this->views,
                'category' => $this->category->name ?? null,
            ],
        ];
    }

    /**
     * Get the search weight/priority for ranking results.
     */
    public function getSearchWeight(): int
    {
        return $this->status === PostStatus::Published ? 70 : 30;
    }
}
