<?php

declare(strict_types=1);

namespace App\Models;

use App\Concerns\Searchable as SearchableTrait;
use App\Contracts\Searchable;
use App\Enums\AccountStatus;
use Datlechin\FilamentMenuBuilder\Concerns\HasMenuPanel;
use Datlechin\FilamentMenuBuilder\Contracts\MenuPanelable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class AccountCategory extends Model implements MenuPanelable, Searchable
{
    use HasFactory;
    use SoftDeletes;
    use HasMenuPanel;
    use SearchableTrait;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'is_visible' => 'bool',
        ];
    }

    public function game(): BelongsTo
    {
        return $this->belongsTo(Game::class);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(AccountCategory::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(AccountCategory::class, 'parent_id');
    }

    public function accounts(): HasMany
    {
        return $this->hasMany(Account::class, 'category_id');
    }

    public function getMenuPanelName(): string
    {
        return 'Danh mục tài khoản';
    }

    public function getMenuPanelTitleColumn(): string
    {
        return 'name';
    }

    public function getMenuPanelUrlUsing(): callable
    {
        return fn(self $model) => route('categories.show', $model->slug);
    }

    public function getAllAccountsCount(): int
    {
        $count = $this->accounts()->where('status', AccountStatus::Selling)->count();

        foreach ($this->children as $child) {
            $count += $child->getAllAccountsCount();
        }

        return $count;
    }

    public static function withAccountCounts(): Builder
    {
        return static::query()
            ->withCount([
                'accounts as remaining_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Selling),
                'accounts as sold_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Sold),
            ])
            ->with(['children' => fn(HasMany $query) => $query->withCount([
                'accounts as remaining_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Selling),
                'accounts as sold_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Sold),
            ])]);
    }

    /**
     * Get the searchable columns for the model.
     */
    public function getSearchableColumns(): array
    {
        return [
            'name',
            'description',
        ];
    }

    /**
     * Get the search result data for the model.
     */
    public function toSearchResult(): array
    {
        return [
            'id' => 'category_' . $this->id,
            'title' => $this->name,
            'description' => $this->description ?? 'Danh mục ' . $this->name,
            'type' => 'category',
            'url' => route('categories.show', $this->slug),
            'image' => $this->image ?? null,
            'category' => $this->game->name ?? 'Danh mục',
            'weight' => $this->is_visible ? 75 : 35,
            'metadata' => [
                'game' => $this->game->name ?? null,
                'parent' => $this->parent->name ?? null,
                'is_visible' => $this->is_visible,
                'accounts_count' => $this->accounts_count ?? 0,
            ],
        ];
    }

    /**
     * Get the search weight/priority for ranking results.
     */
    public function getSearchWeight(): int
    {
        return $this->is_visible ? 75 : 35;
    }
}
