<?php

declare(strict_types=1);

namespace App\Contracts;

use Illuminate\Database\Eloquent\Builder;

interface Searchable
{
    /**
     * Get the searchable columns for the model.
     */
    public function getSearchableColumns(): array;

    /**
     * Apply search query to the model.
     */
    public function scopeSearch(Builder $query, string $term): Builder;

    /**
     * Get the search result data for the model.
     */
    public function toSearchResult(): array;

    /**
     * Get the search weight/priority for ranking results.
     */
    public function getSearchWeight(): int;
}
