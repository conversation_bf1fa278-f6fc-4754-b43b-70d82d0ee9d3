<?php

declare(strict_types=1);

namespace App\Contracts;

use Illuminate\Support\Collection;

interface SearchProvider
{
    /**
     * Search for results using the given query.
     */
    public function search(string $query, int $limit = 10): Collection;

    /**
     * Get the provider name/type.
     */
    public function getProviderName(): string;

    /**
     * Get the search priority for this provider.
     */
    public function getPriority(): int;
}
