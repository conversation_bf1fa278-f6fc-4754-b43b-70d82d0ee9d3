<?php

declare(strict_types=1);

namespace App\Providers;

use App\Contracts\Recharge;
use App\Managers\RechargeManager;
use App\Settings\DepositSettings;
use App\Settings\GeneralSettings;
use App\Settings\SocialLoginSettings;
use App\SocialiteProviders\ZaloProvider;
use Filament\Support\Facades\FilamentColor;
use Illuminate\Foundation\Application;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;
use Laravel\Socialite\Contracts\Factory;

class AppServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(
            Recharge::class,
            fn(Application $app): RechargeManager => new RechargeManager($app, $app->make(DepositSettings::class)),
        );
    }

    public function boot(): void
    {
        JsonResource::withoutWrapping();
        Vite::prefetch(concurrency: 3);

        $this->app->booted(function (Application $app): void {
            $config = $app->make('config');

            $socialite = $app->make(Factory::class);
            $socialite->extend(
                'zalo',
                fn() => $socialite->buildProvider(ZaloProvider::class, $config->get('services.zalo')),
            );

            rescue(function () use ($config, $app): void {
                $generalSettings = $app->make(GeneralSettings::class);
                $socialLoginSettings = $app->make(SocialLoginSettings::class);

                FilamentColor::register([
                    'primary' => $generalSettings->primary_color,
                ]);

                $config->set([
                    'app.name' => $generalSettings->site_name,
                    'app.locale' => $generalSettings->locale,
                    'app.timezone' => $generalSettings->timezone,
                    'services' => [
                        ...$config->get('services'),
                        'facebook' => [
                            ...$config->get('services.facebook'),
                            'client_id' => $socialLoginSettings->facebook_client_id,
                            'client_secret' => $socialLoginSettings->facebook_client_secret,
                        ],
                        'google' => [
                            ...$config->get('services.google'),
                            'client_id' => $socialLoginSettings->google_client_id,
                            'client_secret' => $socialLoginSettings->google_client_secret,
                        ],
                        'zalo' => [
                            ...$config->get('services.zalo'),
                            'client_id' => $socialLoginSettings->zalo_client_id,
                            'client_secret' => $socialLoginSettings->zalo_client_secret,
                        ],
                        'telegram-bot-api' => [
                            'token' => config('services.telegram.bot_token'),
                        ],
                    ],
                    'filament.default_filesystem_disk' => config('filesystems.default'),
                ]);

                date_default_timezone_set($generalSettings->timezone);
                app()->setLocale($generalSettings->locale);
            }, report: false);
        });
    }
}
