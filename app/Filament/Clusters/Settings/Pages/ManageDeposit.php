<?php

declare(strict_types=1);

namespace App\Filament\Clusters\Settings\Pages;

use App\Enums\RechargeProvider;
use App\Facades\Recharge;
use App\Filament\Clusters\Settings;
use App\Forms\Components\CurrencyInput;
use App\Models\BankAccount;
use App\Settings\DepositSettings;
use App\Support\Helper;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Exception;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Pages\SettingsPage;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;

class ManageDeposit extends SettingsPage
{
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';

    protected static string $settings = DepositSettings::class;

    protected static ?string $cluster = Settings::class;

    protected static ?int $navigationSort = 3;

    protected static ?string $title = 'Cấu hình nạp tiền';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->heading('Nạp thẻ cào')
                    ->collapsible()
                    ->persistCollapsed()
                    ->schema([
                        ToggleButtons::make('recharge_type')
                            ->label('Loại nạp')
                            ->inline()
                            ->options([
                                'off' => 'Tắt',
                                'auto' => 'Nạp tự động',
                                'manual' => 'Nạp thủ công',
                            ])
                            ->colors([
                                'off' => 'gray',
                                'auto' => 'success',
                                'manual' => 'info',
                            ])
                            ->reactive()
                            ->columnSpanFull(),
                        Group::make()
                            ->columnSpanFull()
                            ->columns()
                            ->visible(fn(Get $get): bool => $get('recharge_type') === 'auto')
                            ->schema([
                                Select::make('recharge_provider')
                                    ->label('Cổng tích hợp')
                                    ->options(RechargeProvider::class)
                                    ->live()
                                    ->columnSpanFull(),
                                TextInput::make('thesieure_partner_id')
                                    ->label(__('Partner ID'))
                                    ->visible(fn(Get $get): bool => $get('recharge_provider') === RechargeProvider::Thesieure),
                                TextInput::make('thesieure_partner_key')
                                    ->label(__('Partner Key'))
                                    ->visible(fn(Get $get): bool => $get('recharge_provider') === RechargeProvider::Thesieure),
                                TextInput::make('cardvip_partner_id')
                                    ->label(__('Partner ID'))
                                    ->visible(fn(Get $get): bool => $get('recharge_provider') === RechargeProvider::Cardvip),
                                TextInput::make('cardvip_partner_key')
                                    ->label(__('Partner Key'))
                                    ->visible(fn(Get $get): bool => $get('recharge_provider') === RechargeProvider::Cardvip),
                                TextInput::make('thecaosieure_partner_id')
                                    ->label(__('Partner ID'))
                                    ->visible(fn(Get $get): bool => $get('recharge_provider') === RechargeProvider::Thecaosieure),
                                TextInput::make('thecaosieure_partner_key')
                                    ->label(__('Partner Key'))
                                    ->visible(fn(Get $get): bool => $get('recharge_provider') === RechargeProvider::Thecaosieure),
                            ]),
                        Group::make()
                            ->visible(fn(Get $get): bool => $get('recharge_type') !== 'off')
                            ->schema([
                                Repeater::make('recharge_telecoms')
                                    ->label('Loại thẻ')
                                    ->columnSpanFull()
                                    ->collapsible()
                                    ->collapsed()
                                    ->persistCollapsed()
                                    ->itemLabel(fn(array $state): ?string => $state['name'] ?? null)
                                    ->hintAction(
                                        Action::make('fetch')
                                            ->visible(fn(Get $get): bool => $get('recharge_type') === 'auto')
                                            ->label('Lấy dữ liệu đổi thẻ từ API')
                                            ->requiresConfirmation()
                                            ->modalDescription(__('This action will synchronize your current recharge telecoms data with the latest information from the provider you selected. This ensures you have accurate data for recharges. Are you sure you want to proceed?'))
                                            ->action(function (Set $set, Action $action): void {
                                                try {
                                                    $response = Recharge::getFees();

                                                    if ($response->isEmpty()) {
                                                        return;
                                                    }

                                                    $telecoms = collect($response)
                                                        ->transform(fn(Collection $telecom, string $name): array => [
                                                            'name' => ucfirst(strtolower($name)),
                                                            'value' => $name,
                                                            'amounts' => $telecom->transform(fn($amount): array => [
                                                                'value' => $amount['value'],
                                                                'fee' => $amount['fees'],
                                                            ])->all(),
                                                        ])
                                                        ->all();

                                                    $set('recharge_telecoms', array_values($telecoms));
                                                } catch (Exception $e) {
                                                    Notification::make()
                                                        ->danger()
                                                        ->body($e->getMessage())
                                                        ->send();

                                                    $action->cancel();
                                                }
                                            }),
                                    )
                                    ->schema([
                                        TextInput::make('name')
                                            ->label('Tên nhà mạng'),
                                        TextInput::make('value')
                                            ->label('Mã nhà mạng'),
                                        FileUpload::make('logo')
                                            ->image()
                                            ->directory('telecoms'),
                                        Repeater::make('amounts')
                                            ->label('Mệnh giá')
                                            ->collapsed()
                                            ->itemLabel(fn(array $state): string => Helper::formatCurrency((int) $state['value']))
                                            ->schema([
                                                CurrencyInput::make('value')
                                                    ->label('Mệnh giá'),
                                                TextInput::make('fee')
                                                    ->label('Phí đổi thẻ (%)'),
                                            ]),
                                    ]),
                                TextInput::make('recharge_promotion')
                                    ->label('Khuyến mãi nạp thẻ (%)')
                                    ->numeric()
                                    ->minValue(-99)
                                    ->maxValue(100)
                                    ->hintIcon('heroicon-o-question-mark-circle')
                                    ->hintIconTooltip('Ví dụ nếu bạn nhập 10, mỗi lần nạp 100.000đ, khách hàng sẽ nhận được 110.000đ. Ngược lại nếu là -10, khách hàng sẽ nhận được 90.000đ. Nếu bạn không muốn áp dụng khuyến mãi, hãy nhập 0.')
                                    ->columnSpanFull(),
                            ]),
                    ]),
                Section::make()
                    ->heading('Nạp qua ngân hàng')
                    ->collapsible()
                    ->persistCollapsed()
                    ->columns()
                    ->schema([
                        Toggle::make('auto_pay_enabled')
                            ->live()
                            ->label('Cho phép nạp tiền qua ngân hàng'),
                        Group::make()
                            ->columnSpanFull()
                            ->columns()
                            ->visible(fn(Get $get): mixed => $get('auto_pay_enabled'))
                            ->schema([
                                Select::make('auto_pay_default_bank_account')
                                    ->label('Tài khoản ngân hàng mặc định')
                                    ->options(BankAccount::query()->pluck('name', 'id'))
                                    ->helperText('Tài khoản ngân hàng để nhận tiền nạp tự động.')
                                    ->columnSpanFull(),
                                TextInput::make('auto_pay_code_prefix')
                                    ->label('Tiền tố nội dung thanh toán')
                                    ->placeholder('Ví dụ: NAP TIEN')
                                    ->columnSpanFull(),
                                CurrencyInput::make('auto_pay_min_amount')
                                    ->required()
                                    ->label('Số tiền nạp tối thiểu'),
                                CurrencyInput::make('auto_pay_max_amount')
                                    ->required()
                                    ->label('Số tiền nạp tối đa'),
                                TextInput::make('auto_pay_promotion')
                                    ->label('Khuyến mãi nạp tiền (%)')
                                    ->numeric()
                                    ->required()
                                    ->minValue(-99)
                                    ->maxValue(100)
                                    ->hintIcon('heroicon-o-question-mark-circle')
                                    ->hintIconTooltip('Ví dụ nếu bạn nhập 10, mỗi lần nạp 100.000đ, khách hàng sẽ nhận được 110.000đ. Ngược lại nếu là -10, khách hàng sẽ nhận được 90.000đ. Nếu bạn không muốn áp dụng khuyến mãi, hãy nhập 0.')
                                    ->columnSpanFull(),
                            ]),
                    ]),
                Section::make()
                    ->heading('SePay Webhook')
                    ->collapsible()
                    ->persistCollapsed()
                    ->columns()
                    ->schema([
                        TextInput::make('auto_pay_webhook_auth_token')
                            ->label('Mã xác thực SePay Webhook')
                            ->required()
                            ->placeholder('Mã xác thực dùng để xác minh SePay Webhook.')
                            ->hintAction(
                                Action::make('generate')
                                    ->label('Tạo mã xác thực mới')
                                    ->action(fn(Set $set): mixed => $set('auto_pay_webhook_auth_token', bin2hex(random_bytes(16)))),
                            )
                            ->columnSpanFull(),
                        Placeholder::make('SePay Webhook URL')
                            ->label('URL SePay Webhook')
                            ->content(new HtmlString('<code>' . route('webhook.sepay') . '</code>')),
                    ]),
                Section::make()
                    ->heading('Web2M Webhook')
                    ->collapsible()
                    ->persistCollapsed()
                    ->columns()
                    ->schema([
                        TextInput::make('web2m_webhook_auth_token')
                            ->label('Mã xác thực Web2M Webhook')
                            ->required()
                            ->placeholder('Mã xác thực dùng để xác minh Web2M Webhook.')
                            ->hintAction(
                                Action::make('generate')
                                    ->label('Tạo mã xác thực mới')
                                    ->action(fn(Set $set): mixed => $set('web2m_webhook_auth_token', bin2hex(random_bytes(16)))),
                            )
                            ->columnSpanFull(),
                        Placeholder::make('Web2M Webhook URL')
                            ->label('URL Web2M Webhook')
                            ->content(new HtmlString('<code>' . route('webhook.web2m') . '</code>')),
                    ]),
            ]);
    }

    protected function beforeSave(): void
    {
        if (app()->environment('demo')) {
            Notification::make()
                ->warning()
                ->body('Không thể thay đổi cấu hình trong môi trường demo.')
                ->send();

            $this->halt();
        }
    }
}
