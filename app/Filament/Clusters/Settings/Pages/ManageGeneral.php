<?php

declare(strict_types=1);

namespace App\Filament\Clusters\Settings\Pages;

use App\Filament\Clusters\Settings;
use App\Settings\GeneralSettings;
use App\Support\Helper;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Forms\Components\ColorPicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage as PagesSettingsPage;

class ManageGeneral extends PagesSettingsPage
{
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static string $settings = GeneralSettings::class;

    protected static ?string $cluster = Settings::class;

    protected static ?int $navigationSort = 1;

    protected static ?string $title = 'Cài đặt chung';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        TextInput::make('site_name')
                            ->label('Tên trang web')
                            ->required()
                            ->columnSpanFull(),
                        TextInput::make('site_title')
                            ->label('Tiêu đề trang web')
                            ->required()
                            ->columnSpanFull(),
                        TagsInput::make('admin_email')
                            ->label('Email quản trị viên')
                            ->nestedRecursiveRules('email')
                            ->columnSpanFull(),
                        FileUpload::make('favicon')
                            ->image(),
                        FileUpload::make('logo')
                            ->image(),
                        ColorPicker::make('primary_color')
                            ->label('Màu chủ đạo')
                            ->required(),
                        ColorPicker::make('primary_text_color')
                            ->label('Màu chữ chủ đạo'),
                        Select::make('primary_font')
                            ->label('Font chữ chủ đạo')
                            ->options(array_combine($fonts = Helper::getFonts(), $fonts))
                            ->searchable()
                            ->required(),
                        Repeater::make('banner_sliders')
                            ->label('Ảnh quảng cáo')
                            ->columnSpanFull()
                            ->itemLabel(fn(array $state): ?string => $state['label'])
                            ->schema([
                                TextInput::make('label')
                                    ->label('Tiêu đề'),
                                FileUpload::make('image')
                                    ->label('Hình ảnh')
                                    ->label('Ảnh')
                                    ->image()
                                    ->required(),
                                TextInput::make('url')
                                    ->label(__('URL'))
                                    ->url(),
                                Toggle::make('open_in_new_tab')
                                    ->label('Mở trong tab mới'),
                            ]),
                    ])
                    ->columns(),
            ]);
    }
}
