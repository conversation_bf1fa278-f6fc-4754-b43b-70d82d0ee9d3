<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\DepositResource\Pages;
use App\Models\BankAccount;
use App\Models\Deposit;
use App\Models\Recharge;
use App\Tables\Columns\DateTimeColumn;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;

class DepositResource extends Resource
{
    protected static ?string $model = Deposit::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $modelLabel = 'Lịch sử nạp tiền';

    protected static ?string $navigationGroup = 'Quản lý Tà<PERSON> chính';

    protected static ?int $navigationSort = 1;

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->schema([
                        TextEntry::make('user.display_name')
                            ->label('Người dùng')
                            ->url(fn(Deposit $deposit): string => UserResource::getUrl('edit', [$deposit->user])),
                        TextEntry::make('source_name')
                            ->label('Nguồn')
                            ->url(fn(Deposit $deposit): ?string => match ($deposit->source_type) {
                                BankAccount::class => BankAccountResource::getUrl('edit', [$deposit->source]),
                                Recharge::class => RechargeResource::getUrl('view', [$deposit->source]),
                                default => null,
                            }),
                        TextEntry::make('provider')
                            ->visible(fn(Deposit $deposit): bool => ! empty($deposit->provider))
                            ->label('Cổng tích hợp'),
                        TextEntry::make('transaction_id')
                            ->visible(fn(Deposit $deposit): bool => ! empty($deposit->transaction_id))
                            ->label('Mã giao dịch'),
                        TextEntry::make('amount')
                            ->label('Số tiền')
                            ->money(),
                        TextEntry::make('content')
                            ->label('Nội dung'),
                        TextEntry::make('status')
                            ->badge()
                            ->label('Trạng thái'),
                        TextEntry::make('transacted_at')
                            ->label('Hoàn thành lúc')
                            ->dateTime(),
                        TextEntry::make('created_at')
                            ->label('Tạo lúc')
                            ->dateTime(),
                        TextEntry::make('updated_at')
                            ->label('Cập nhật lúc')
                            ->dateTime(),
                        TextEntry::make('raw')
                            ->label('Dữ liệu gốc')
                            ->formatStateUsing(fn(Deposit $record): HtmlString => new HtmlString('<pre>' . json_encode($record->raw, JSON_PRETTY_PRINT) . '</pre>'))
                            ->columnSpanFull(),
                    ])
                    ->columns(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.display_name')
                    ->label('Người dùng')
                    ->sortable(),
                Tables\Columns\TextColumn::make('provider')
                    ->label('Cổng tích hợp')
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount')
                    ->label('Số tiền')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->label('Trạng thái')
                    ->searchable(),
                Tables\Columns\TextColumn::make('transacted_at')
                    ->label('Hoàn thành lúc')
                    ->dateTime()
                    ->sortable(),
                DateTimeColumn::make('created_at'),
                DateTimeColumn::make('updated_at'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDeposits::route('/'),
            'view' => Pages\ViewDeposit::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
