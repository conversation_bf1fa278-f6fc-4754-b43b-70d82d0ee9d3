<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Models\AuthenticationLog;
use BezhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Carbon;

class LatestAuthenticationLog extends BaseWidget
{
    use HasWidgetShield;

    use InteractsWithPageFilters;

    protected static ?string $heading = 'Nhật ký đăng nhập';

    protected static ?int $sort = 20;

    public function table(Table $table): Table
    {
        [$startDate, $endDate] = explode(' - ', $this->filters['date_range']);

        return $table
            ->query(
                AuthenticationLog::query()
                    ->whereDate('login_at', '>=', value: Carbon::createFromFormat('d/m/Y', $startDate)->startOfDay())
                    ->whereDate('login_at', '<=', Carbon::createFromFormat('d/m/Y', $endDate)->endOfDay())
                    ->whereNotNull('login_at')
                    ->with('authenticatable')
                    ->latest('id'),
            )
            ->columns([
                Tables\Columns\TextColumn::make('authenticatable_type')
                    ->label('Người dùng')
                    ->formatStateUsing(fn(AuthenticationLog $authenticationLog) => $authenticationLog->authenticatable->getFilamentName()),
                Tables\Columns\TextColumn::make('ip_address')
                    ->url(fn(string $state): string => "https://ipinfo.io/$state", true)
                    ->label('Địa chỉ IP'),
                Tables\Columns\TextColumn::make('login_at')
                    ->since()
                    ->label('Đăng nhập lúc'),
            ]);
    }
}
