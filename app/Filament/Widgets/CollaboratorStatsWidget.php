<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Enums\AccountStatus;
use App\Enums\TransactionType;
use App\Models\Account;
use App\Models\Transaction;
use App\Support\Helper;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Carbon\Carbon;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class CollaboratorStatsWidget extends BaseWidget
{
    use HasWidgetShield;
    use InteractsWithPageFilters;

    protected function getStats(): array
    {
        $user = Auth::user();
        [$startDate, $endDate] = explode(' - ', $this->filters['date_range']);

        return [
            Stat::make('Tổng doanh thu', Helper::formatCurrency(Transaction::query()
                ->where('type', TransactionType::SellAccount)
                ->whereBelongsTo($user)
                ->whereDate('created_at', '>=', Carbon::createFromFormat('d/m/Y', $startDate)->startOfDay())
                ->whereDate('created_at', '<=', Carbon::createFromFormat('d/m/Y', $endDate)->endOfDay())
                ->sum('amount'))),
            Stat::make('Số dư', Helper::formatCurrency($user->balance)),
            Stat::make(
                'Tài khoản đang bán',
                number_format((int) Account::query()
                    ->whereBelongsTo($user)
                    ->where('status', AccountStatus::Selling)
                    ->count()),
            ),
            Stat::make(
                'Tài khoản đã bán',
                number_format((int) Account::query()
                    ->whereBelongsTo($user)
                    ->where('status', AccountStatus::Sold)
                    ->count()),
            ),
        ];
    }
}
