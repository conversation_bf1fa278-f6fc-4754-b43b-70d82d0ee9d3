<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Filament\Resources\TransactionResource;
use App\Models\Transaction;
use App\Support\Helper;
use BezhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Carbon;

class LatestTransactions extends BaseWidget
{
    use HasWidgetShield;
    use InteractsWithPageFilters;

    protected static ?string $heading = 'Giao dịch gần đây';

    protected static ?int $sort = 10;

    public function table(Table $table): Table
    {
        [$startDate, $endDate] = explode(' - ', $this->filters['date_range']);

        return $table
            ->query(
                Transaction::query()
                    ->whereDate('created_at', '>=', Carbon::createFromFormat('d/m/Y', $startDate)->startOfDay())
                    ->whereDate('created_at', '<=', Carbon::createFromFormat('d/m/Y', $endDate)->endOfDay())
                    ->latest(),
            )
            ->columns([
                Tables\Columns\TextColumn::make('user.username')
                    ->label('Người dùng'),
                Tables\Columns\TextColumn::make('type')
                    ->url(fn(Transaction $record): string => TransactionResource::getUrl('view', [$record]))
                    ->label('Giao dịch'),
                Tables\Columns\TextColumn::make('amount')
                    ->label('Số tiền')
                    ->color(fn(Transaction $record): string => $record->type->isPositive() ? 'success' : 'danger')
                    ->formatStateUsing(function (int $state, Transaction $record): string {
                        $amount = Helper::formatCurrency($state);

                        return $record->type->isPositive() ? "+$amount" : "-$amount";
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->since()
                    ->label('Thời gian'),
            ]);
    }
}
