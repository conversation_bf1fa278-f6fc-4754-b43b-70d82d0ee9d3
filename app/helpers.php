<?php

declare(strict_types=1);

use App\Services\ColorService;

if (! function_exists('get_primary_color_variables')) {
    function get_primary_color_variables(string $hex, ?string $textColor = null): array
    {
        return ColorService::generatePrimaryColorVariables($hex, $textColor);
    }
}

if (! function_exists('get_contrast_color')) {
    function get_contrast_color(string $hex): string
    {
        return ColorService::getContrastColor($hex);
    }
}

if (! function_exists('hex_to_hsl')) {
    function hex_to_hsl(string $hex): string
    {
        return ColorService::hexToHslString($hex);
    }
}
