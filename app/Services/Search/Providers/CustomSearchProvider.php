<?php

declare(strict_types=1);

namespace App\Services\Search\Providers;

use App\Contracts\SearchProvider;
use App\DataTransferObjects\SearchResult;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class CustomSearchProvider implements SearchProvider
{
    /**
     * @var Collection<SearchResult>
     */
    private Collection $customItems;

    public function __construct()
    {
        $this->customItems = collect();
    }

    /**
     * Add a custom search item.
     */
    public function addCustomItem(SearchResult $item): void
    {
        $this->customItems->push($item);
    }

    public function search(string $query, int $limit = 10): Collection
    {
        if ($this->customItems->isEmpty()) {
            $this->registerDefaultItems();
        }

        $query = Str::lower(trim($query));

        if (empty($query)) {
            return collect();
        }

        $results = $this->customItems
            ->filter(function (SearchResult $item) use ($query) {
                $title = $this->normalizeVietnamese(Str::lower($item->title));
                $description = $this->normalizeVietnamese(Str::lower($item->description));
                $category = $this->normalizeVietnamese(Str::lower($item->category ?? ''));
                $normalizedQuery = $this->normalizeVietnamese($query);

                $matches = Str::contains($title, $query)
                    || Str::contains($description, $query)
                    || Str::contains($category, $query)
                    || Str::contains($title, $normalizedQuery)
                    || Str::contains($description, $normalizedQuery)
                    || Str::contains($category, $normalizedQuery);

                return $matches;
            })
            ->take($limit)
            ->values();

        return $results;
    }

    public function getProviderName(): string
    {
        return 'custom';
    }

    public function getPriority(): int
    {
        return 50;
    }

    /**
     * Register default custom search items.
     */
    private function registerDefaultItems(): void
    {
        if (!app()->routesAreCached() || app()->bound('router')) {
            try {
                $this->addCustomItem(new SearchResult(
                    id: 'login',
                    title: 'Đăng nhập',
                    description: 'Đăng nhập vào tài khoản của bạn',
                    type: 'navigation',
                    url: route('login'),
                    category: 'Tài khoản',
                    weight: 80
                ));

                $this->addCustomItem(new SearchResult(
                    id: 'register',
                    title: 'Đăng ký',
                    description: 'Tạo tài khoản mới',
                    type: 'navigation',
                    url: route('register'),
                    category: 'Tài khoản',
                    weight: 80
                ));

                $this->addCustomItem(new SearchResult(
                    id: 'deposit',
                    title: 'Nạp tiền',
                    description: 'Nạp tiền vào tài khoản',
                    type: 'navigation',
                    url: route('user.deposit.index'),
                    category: 'Giao dịch',
                    weight: 90
                ));

                $this->addCustomItem(new SearchResult(
                    id: 'profile',
                    title: 'Thông tin tài khoản',
                    description: 'Xem và chỉnh sửa thông tin cá nhân',
                    type: 'navigation',
                    url: route('user.profile'),
                    category: 'Tài khoản',
                    weight: 70
                ));

                $this->addCustomItem(new SearchResult(
                    id: 'transactions',
                    title: 'Lịch sử giao dịch',
                    description: 'Xem lịch sử các giao dịch đã thực hiện',
                    type: 'navigation',
                    url: route('user.transactions'),
                    category: 'Giao dịch',
                    weight: 60
                ));

                $this->addCustomItem(new SearchResult(
                    id: 'purchased-accounts',
                    title: 'Tài khoản đã mua',
                    description: 'Danh sách tài khoản game đã mua',
                    type: 'navigation',
                    url: route('user.purchased-accounts.index'),
                    category: 'Tài khoản game',
                    weight: 70
                ));
            } catch (\Exception) {
            }
        }
    }

    /**
     * Get all custom items.
     */
    public function getCustomItems(): Collection
    {
        return $this->customItems;
    }

    /**
     * Normalize Vietnamese text by removing diacritics.
     */
    private function normalizeVietnamese(string $text): string
    {
        $vietnamese = [
            'à',
            'á',
            'ạ',
            'ả',
            'ã',
            'â',
            'ầ',
            'ấ',
            'ậ',
            'ẩ',
            'ẫ',
            'ă',
            'ằ',
            'ắ',
            'ặ',
            'ẳ',
            'ẵ',
            'è',
            'é',
            'ẹ',
            'ẻ',
            'ẽ',
            'ê',
            'ề',
            'ế',
            'ệ',
            'ể',
            'ễ',
            'ì',
            'í',
            'ị',
            'ỉ',
            'ĩ',
            'ò',
            'ó',
            'ọ',
            'ỏ',
            'õ',
            'ô',
            'ồ',
            'ố',
            'ộ',
            'ổ',
            'ỗ',
            'ơ',
            'ờ',
            'ớ',
            'ợ',
            'ở',
            'ỡ',
            'ù',
            'ú',
            'ụ',
            'ủ',
            'ũ',
            'ư',
            'ừ',
            'ứ',
            'ự',
            'ử',
            'ữ',
            'ỳ',
            'ý',
            'ỵ',
            'ỷ',
            'ỹ',
            'đ',
            'À',
            'Á',
            'Ạ',
            'Ả',
            'Ã',
            'Â',
            'Ầ',
            'Ấ',
            'Ậ',
            'Ẩ',
            'Ẫ',
            'Ă',
            'Ằ',
            'Ắ',
            'Ặ',
            'Ẳ',
            'Ẵ',
            'È',
            'É',
            'Ẹ',
            'Ẻ',
            'Ẽ',
            'Ê',
            'Ề',
            'Ế',
            'Ệ',
            'Ể',
            'Ễ',
            'Ì',
            'Í',
            'Ị',
            'Ỉ',
            'Ĩ',
            'Ò',
            'Ó',
            'Ọ',
            'Ỏ',
            'Õ',
            'Ô',
            'Ồ',
            'Ố',
            'Ộ',
            'Ổ',
            'Ỗ',
            'Ơ',
            'Ờ',
            'Ớ',
            'Ợ',
            'Ở',
            'Ỡ',
            'Ù',
            'Ú',
            'Ụ',
            'Ủ',
            'Ũ',
            'Ư',
            'Ừ',
            'Ứ',
            'Ự',
            'Ử',
            'Ữ',
            'Ỳ',
            'Ý',
            'Ỵ',
            'Ỷ',
            'Ỹ',
            'Đ'
        ];

        $latin = [
            'a',
            'a',
            'a',
            'a',
            'a',
            'a',
            'a',
            'a',
            'a',
            'a',
            'a',
            'a',
            'a',
            'a',
            'a',
            'a',
            'a',
            'e',
            'e',
            'e',
            'e',
            'e',
            'e',
            'e',
            'e',
            'e',
            'e',
            'e',
            'i',
            'i',
            'i',
            'i',
            'i',
            'o',
            'o',
            'o',
            'o',
            'o',
            'o',
            'o',
            'o',
            'o',
            'o',
            'o',
            'o',
            'o',
            'o',
            'o',
            'o',
            'o',
            'u',
            'u',
            'u',
            'u',
            'u',
            'u',
            'u',
            'u',
            'u',
            'u',
            'u',
            'y',
            'y',
            'y',
            'y',
            'y',
            'd',
            'A',
            'A',
            'A',
            'A',
            'A',
            'A',
            'A',
            'A',
            'A',
            'A',
            'A',
            'A',
            'A',
            'A',
            'A',
            'A',
            'A',
            'E',
            'E',
            'E',
            'E',
            'E',
            'E',
            'E',
            'E',
            'E',
            'E',
            'E',
            'I',
            'I',
            'I',
            'I',
            'I',
            'O',
            'O',
            'O',
            'O',
            'O',
            'O',
            'O',
            'O',
            'O',
            'O',
            'O',
            'O',
            'O',
            'O',
            'O',
            'O',
            'O',
            'U',
            'U',
            'U',
            'U',
            'U',
            'U',
            'U',
            'U',
            'U',
            'U',
            'U',
            'Y',
            'Y',
            'Y',
            'Y',
            'Y',
            'D'
        ];

        return str_replace($vietnamese, $latin, $text);
    }
}
