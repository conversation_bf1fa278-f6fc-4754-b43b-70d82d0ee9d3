<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\GameResource;
use App\Models\Game;
use Inertia\Inertia;
use Inertia\Response;

class GameController extends Controller
{
    public function index(): Response
    {
        $games = Game::query()
            ->where('is_visible', true)
            ->with(['publisher', 'categories' => fn($query) => $query->whereNull('parent_id')->with('children')])
            ->get();

        return Inertia::render('Games/Index', [
            'games' => GameResource::collection($games),
        ]);
    }

    public function show(string $slug): Response
    {
        $game = Game::query()
            ->where('slug', $slug)
            ->where('is_visible', true)
            ->with([
                'publisher',
                'categories' => fn($query) => $query->whereNull('parent_id')->with(['children', 'accounts']),
            ])
            ->firstOrFail();

        return Inertia::render('Games/Show', [
            'game' => new GameResource($game),
        ]);
    }
}
