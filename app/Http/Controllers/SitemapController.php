<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\AccountStatus;
use App\Models\Account;
use App\Models\AccountCategory;
use App\Models\Game;
use App\Models\Post;
use App\Models\Wheel;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;

class SitemapController extends Controller
{
    public function index(): Response
    {
        $content = Cache::remember('sitemap.xml', 3600, function () {
            return view('sitemap.index', [
                'games' => Game::query()->get(),
                'categories' => AccountCategory::query()->get(),
                'posts' => Post::query()->get(),
                'wheels' => Wheel::query()->get(),
                'accounts' => Account::query()->whereIn('status', [AccountStatus::Selling, AccountStatus::Sold])->get(),
            ])->render();
        });

        return response($content, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    }
}
