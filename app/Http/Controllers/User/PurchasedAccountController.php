<?php

declare(strict_types=1);

namespace App\Http\Controllers\User;

use App\Enums\AccountStatus;
use App\Enums\PriceRange;
use App\Http\Controllers\Controller;
use App\Http\Resources\PurchasedAccountResource;
use App\Models\AccountCategory;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class PurchasedAccountController extends Controller
{
    public function index(Request $request): Response
    {
        $request->validate([
            'id' => ['nullable', 'string'],
            'category_id' => ['nullable', 'string', Rule::exists(AccountCategory::class, 'id')],
            'price' => ['nullable', 'string'],
            'from_date' => ['nullable', 'date'],
            'to_date' => ['nullable', 'date'],
        ]);

        $accounts = $request->user()
            ->purchasedAccounts()
            ->whereHas('account', fn(Builder $query) => $query->where('status', AccountStatus::Sold))
            ->with('account.category')
            ->when($request->input('id'), fn(Builder $query, string $id) => $query->where('account_id', 'like', "%$id%"))
            ->when($request->input('category_id'), fn(Builder $query, string $id) => $query->whereRelation('account.category', 'id', $id))
            ->when($request->input('price'), fn(Builder $query, string $price): Builder => match (PriceRange::tryFrom($price)) {
                PriceRange::BELOW_50K => $query->where('price', '<', 50_000),
                PriceRange::FROM_50K_TO_200K => $query->whereBetween('price', [50_000, 200_000]),
                PriceRange::FROM_200K_TO_500K => $query->whereBetween('price', [200_000, 500_000]),
                PriceRange::FROM_500K_TO_1M => $query->whereBetween('price', [500_000, 1_000_000]),
                PriceRange::FROM_1M_TO_5M => $query->whereBetween('price', [1_000_000, 5_000_000]),
                PriceRange::FROM_5M_TO_10M => $query->whereBetween('price', [5_000_000, 10_000_000]),
                PriceRange::ABOVE_10M => $query->where('price', '>', 10_000_000),
                default => $query,
            })
            ->when(
                $request->input('from_date'),
                fn(Builder $query, string $fromDate) => $query->whereDate('created_at', '>=', $fromDate),
            )
            ->when(
                $request->input('to_date'),
                fn(Builder $query, string $toDate) => $query->whereDate('created_at', '<=', $toDate),
            )
            ->latest('created_at')
            ->paginate();

        $categories = AccountCategory::query()->pluck('name', 'id');

        return Inertia::render('User/PurchasedAccount', [
            'categories' => $categories,
            'accounts' => PurchasedAccountResource::collection($accounts),
            'priceRanges' => collect(PriceRange::cases())
                ->mapWithKeys(fn(PriceRange $priceRange) => [$priceRange->value => $priceRange->getLabel()]),
            'filters' => $request->query(),
        ]);
    }

    public function show(string $account, Request $request): \App\Http\Resources\PurchasedAccountResource
    {
        $account = $request->user()
            ->purchasedAccounts()
            ->withWhereHas('account', fn(Builder $query) => $query->where('id', $account))
            ->firstOrFail();

        if ($account->seen_at === null) {
            $account->update(['seen_at' => now()]);
        }

        return new PurchasedAccountResource($account);
    }
}
