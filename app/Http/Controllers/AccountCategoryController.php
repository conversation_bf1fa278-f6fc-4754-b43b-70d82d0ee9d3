<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\AccountStatus;
use App\Enums\PriceRange;
use App\Http\Resources\AccountResource;
use App\Http\Resources\AccountCategoryResource;
use App\Models\Account;
use App\Models\AttributeSet;
use App\Models\AccountCategory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class AccountCategoryController extends Controller
{
    public function show(string $slug, Request $request): Response
    {
        $request->validate([
            'search' => ['nullable', 'string'],
            'sort_by' => ['nullable', 'string'],
            'price' => ['nullable', 'string'],
            'attributes' => ['nullable', 'array'],
            'attributes.*' => ['required', 'string'],
            'category_search' => ['nullable', 'string'],
        ]);

        $category = AccountCategory::query()
            ->where('slug', $slug)
            ->where('is_visible', true)
            ->withCount([
                'accounts as remaining_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Selling),
                'accounts as sold_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Sold),
            ])
            ->with(['game', 'parent', 'children' => function (HasMany $query) use ($request) {
                $query
                    ->withCount([
                        'accounts as remaining_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Selling),
                        'accounts as sold_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Sold),
                    ])
                    ->when($request->input('category_search'), function (Builder $query, string $search) {
                        $query->where('name', 'like', "%{$search}%");
                    })
                    ->with('children', function (HasMany $query) use ($request) {
                        $query->withCount([
                            'accounts as remaining_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Selling),
                            'accounts as sold_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Sold),
                        ])
                            ->when($request->input('category_search'), function (Builder $query, string $search) {
                                $query->where('name', 'like', "%{$search}%");
                            });
                    });
            }])
            ->firstOrFail();

        $attributeSets = AttributeSet::query()
            ->whereBelongsTo($category, 'category')
            ->where('is_visible', true)
            ->with('attributes')
            ->get();

        $children = $category->children->map(function ($child) {
            return new AccountCategoryResource($child);
        });

        $accounts = Account::query()
            ->whereBelongsTo($category, 'category')
            ->where('status', AccountStatus::Selling)
            ->when(
                $request->input('search'),
                fn(Builder $query, string $search): Builder => $query
                    ->where(function (Builder $query) use ($search): void {
                        $query
                            ->where('id', 'like', "%$search%")
                            ->orWhere('description', 'like', "%$search%");
                    }),
            )
            ->when($request->input('attributes'), function (Builder $query, array $attributes): Builder {
                foreach ($attributes as $set => $attribute) {
                    $query->whereHas('attributes', function (Builder $query) use ($set, $attribute): void {
                        $query
                            ->join('attribute_sets', 'attribute_sets.id', 'attributes.attribute_set_id')
                            ->where('attribute_sets.slug', $set)
                            ->where('attributes.slug', $attribute);
                    });
                }

                return $query;
            })
            ->when($request->input('sort_by'), fn(Builder $query, string $sortBy): Builder => match ($sortBy) {
                'price-asc' => $query->orderBy('price'),
                'price-desc' => $query->orderByDesc('price'),
                'newest' => $query->latest(),
                'oldest' => $query->oldest(),
                default => $query,
            })
            ->when($request->input('price'), fn(Builder $query, string $price): Builder => match (PriceRange::tryFrom($price)) {
                PriceRange::BELOW_50K => $query->where('price', '<', 50_000),
                PriceRange::FROM_50K_TO_200K => $query->whereBetween('price', [50_000, 200_000]),
                PriceRange::FROM_200K_TO_500K => $query->whereBetween('price', [200_000, 500_000]),
                PriceRange::FROM_500K_TO_1M => $query->whereBetween('price', [500_000, 1_000_000]),
                PriceRange::FROM_1M_TO_5M => $query->whereBetween('price', [1_000_000, 5_000_000]),
                PriceRange::FROM_5M_TO_10M => $query->whereBetween('price', [5_000_000, 10_000_000]),
                PriceRange::ABOVE_10M => $query->where('price', '>', 10_000_000),
                default => $query,
            })
            ->with([
                'attributes' => fn(BelongsToMany $query) => $query
                    ->where('is_visible', true)
                    ->select('id', 'attribute_set_id', 'name', 'slug'),
                'attributes.attributeSet:id,name,slug',
                'flashSales' => fn(BelongsToMany $query) => $query->available(),
            ])
            ->paginate(24);

        $responseData = [
            'category' => new AccountCategoryResource($category),
            'children' => $children,
            'attributeSets' => $attributeSets,
            'priceRanges' => collect(PriceRange::cases())
                ->mapWithKeys(fn(PriceRange $priceRange) => [$priceRange->value => $priceRange->getLabel()]),
            'filters' => $request->query(),
        ];

        if ($request->has('category_search')) {
            return Inertia::render('AccountCategories/CategoryShow', $responseData);
        }

        if ($children->isEmpty()) {
            $responseData['accounts'] = AccountResource::collection($accounts);
            return Inertia::render('AccountCategories/AccountShow', $responseData);
        } else {
            if ($accounts->total() > 0 || $request->hasAny(['search', 'sort_by', 'price', 'attributes']) || $attributeSets->isNotEmpty()) {
                $responseData['accounts'] = AccountResource::collection($accounts);
                return Inertia::render('AccountCategories/AccountShow', $responseData);
            } else {
                return Inertia::render('AccountCategories/CategoryShow', $responseData);
            }
        }
    }
}
