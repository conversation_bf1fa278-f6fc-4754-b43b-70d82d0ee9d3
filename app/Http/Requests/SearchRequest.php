<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'query' => [
                'required',
                'string',
                'min:1',
                'max:255',
                'regex:/^[a-zA-Z0-9\s\p{L}\p{M}\p{N}\p{P}\p{S}]+$/u', // Allow letters, numbers, spaces, and common symbols
            ],
            'limit' => [
                'sometimes',
                'integer',
                'min:1',
                'max:50',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'query.required' => 'Vui lòng nhập từ khóa tìm kiếm.',
            'query.min' => 'Từ khóa tìm kiếm phải có ít nhất 1 ký tự.',
            'query.max' => 'Từ khóa tìm kiếm không được vượt quá 255 ký tự.',
            'query.regex' => 'Từ khóa tìm kiếm chứa ký tự không hợp lệ.',
            'limit.integer' => 'Số lượng kết quả phải là số nguyên.',
            'limit.min' => 'Số lượng kết quả phải ít nhất là 1.',
            'limit.max' => 'Số lượng kết quả không được vượt quá 50.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'query' => trim($this->input('query', '')),
        ]);
    }
}
