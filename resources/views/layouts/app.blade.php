<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title inertia>{{ config('app.name', 'Laravel') }}</title>

        @php
            $generalSettings = app(\App\Settings\GeneralSettings::class);
            $colorVariables = get_primary_color_variables($generalSettings->primary_color, $generalSettings->primary_text_color);
        @endphp

        @if($generalSettings->favicon)
            <link rel="icon" href="{{ Storage::url($generalSettings->favicon) }}" type="image/x-icon">
        @endif

        @routes
        @vite(['resources/js/app.ts', "resources/js/Pages/{$page['component']}.vue"])
        @inertiaHead

        {{ filament()->getFontHtml() }}

        <style>
            :root {
                --primary-font: '{!! filament()->getFontFamily() !!}';
                --primary-color: {{ $generalSettings->primary_color }};
                --primary-color-hsl: {{ $colorVariables['primary_hsl'] }};
                --primary-foreground-hsl: {{ $colorVariables['primary_foreground_hsl'] }};
            }
        </style>
    </head>
    <body class="bg-background">
        @inertia
    </body>
</html>
