<script setup lang="ts">
import ArrowLeftEndOnRectangleIcon from '@/components/Icons/ArrowLeftEndOnRectangleIcon.vue'
import ArrowTopRightOnSquare from '@/components/Icons/ArrowTopRightOnSquare.vue'
import BankNotesIcon from '@/components/Icons/BankNotesIcon.vue'
import CreditCardIcon from '@/components/Icons/CreditCardIcon.vue'
import HomeIcon from '@/components/Icons/HomeIcon.vue'
import UserCircleIcon from '@/components/Icons/UserCircleIcon.vue'
import ThemeToggle from '@/components/ThemeToggle.vue'
import { GlobalSearch } from '@/components/Search'
import { Avatar, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuShortcut,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
    NavigationMenu,
    NavigationMenuList,
    navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu'
import { Link, usePage } from '@inertiajs/vue3'
import { computed } from 'vue'
import AdWidget from './AdWidget.vue'
import NavigationMenuContent from './ui/navigation-menu/NavigationMenuContent.vue'
import NavigationMenuItem from './ui/navigation-menu/NavigationMenuItem.vue'
import NavigationMenuLink from './ui/navigation-menu/NavigationMenuLink.vue'
import NavigationMenuTrigger from './ui/navigation-menu/NavigationMenuTrigger.vue'

const menu = usePage().props.app.menu
const user = computed(() => usePage().props.auth.user)
</script>

<template>
    <header
        class="sticky top-0 z-20 border-b shadow-xs bg-background backdrop-blur-lg"
    >
        <div
            class="container flex items-center justify-between h-16 py-2 sm:py-4"
        >
            <div class="flex items-center gap-20">
                <div class="flex items-center gap-2">
                    <Link :href="route('home')" class="block max-w-44">
                        <img
                            v-if="$page.props.app.logo"
                            v-lazy="$page.props.app.logo"
                            alt="page.props.app.name"
                            class="w-auto max-h-20 sm:max-h-32"
                        />
                        <span
                            class="text-2xl font-medium transition-all text-primary hover:text-primary/90"
                            v-else
                        >
                            {{ $page.props.app.name }}
                        </span>
                    </Link>
                </div>
                <NavigationMenu class="hidden lg:block">
                    <NavigationMenuList>
                        <NavigationMenuItem
                            v-for="(item, index) in menu"
                            :key="index"
                        >
                            <template v-if="item.children.length > 0">
                                <NavigationMenuTrigger>
                                    {{ item.title }}
                                </NavigationMenuTrigger>
                                <NavigationMenuContent>
                                    <div className="w-[400px] p-2">
                                        <NavigationMenuLink
                                            as-child
                                            v-for="(
                                                child, index
                                            ) in item.children"
                                            :key="index"
                                        >
                                            <Link
                                                :href="child.url"
                                                :class="
                                                    navigationMenuTriggerStyle()
                                                "
                                                class="justify-start! min-w-full"
                                            >
                                                {{ child.title }}
                                            </Link>
                                        </NavigationMenuLink>
                                    </div>
                                </NavigationMenuContent>
                            </template>
                            <NavigationMenuLink as-child v-else>
                                <Link
                                    :href="item.url"
                                    :class="navigationMenuTriggerStyle()"
                                >
                                    {{ item.title }}
                                </Link>
                            </NavigationMenuLink>
                        </NavigationMenuItem>
                    </NavigationMenuList>
                </NavigationMenu>
            </div>

            <!-- Search Component -->
            <div class="flex-1 max-w-md mx-4 hidden md:block">
                <GlobalSearch />
            </div>

            <div class="flex items-center gap-4">
                <!-- Mobile Search Button -->
                <div class="md:hidden">
                    <GlobalSearch />
                </div>

                <ThemeToggle />

                <DropdownMenu v-if="user">
                    <DropdownMenuTrigger class="leading-none">
                        <Avatar class="size-8">
                            <AvatarImage
                                :src="user.avatar_url"
                                :alt="user.display_name"
                            />
                        </Avatar>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuLabel>
                            <div class="flex items-center gap-2">
                                <Avatar>
                                    <AvatarImage
                                        :src="user.avatar_url"
                                        :alt="user.display_name"
                                    />
                                </Avatar>
                                <div class="grid">
                                    <h3
                                        class="font-medium truncate"
                                        :title="user.display_name"
                                    >
                                        {{ user.display_name }}
                                    </h3>
                                    <div
                                        class="flex items-center gap-1 text-sm font-medium text-muted-foreground"
                                    >
                                        <BankNotesIcon class="size-4" />
                                        {{ user.formatted_balance }}
                                    </div>
                                </div>
                            </div>
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuGroup>
                            <DropdownMenuItem
                                v-if="$page.props.app.admin_panel_url"
                                as-child
                            >
                                <a
                                    :href="$page.props.app.admin_panel_url"
                                    class="flex w-full gap-2 cursor-pointer"
                                    target="_blank"
                                >
                                    <HomeIcon class="size-5" />
                                    <span>Bảng quản trị</span>
                                    <DropdownMenuShortcut>
                                        <ArrowTopRightOnSquare class="size-4" />
                                    </DropdownMenuShortcut>
                                </a>
                            </DropdownMenuItem>
                            <DropdownMenuItem as-child>
                                <Link
                                    :href="route('user.profile')"
                                    class="flex w-full gap-2 cursor-pointer"
                                >
                                    <UserCircleIcon class="size-5" />
                                    <span>Quản lý tài khoản</span>
                                </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem as-child>
                                <Link
                                    :href="route('user.deposit.index')"
                                    class="flex w-full gap-2 cursor-pointer"
                                >
                                    <CreditCardIcon class="size-5" />
                                    <span>Nạp tiền</span>
                                </Link>
                            </DropdownMenuItem>
                        </DropdownMenuGroup>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem as-child>
                            <Link
                                :href="route('logout')"
                                as="button"
                                method="post"
                                class="flex w-full gap-2 cursor-pointer"
                            >
                                <ArrowLeftEndOnRectangleIcon class="size-5" />
                                <span>Đăng xuất</span>
                            </Link>
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
                <template v-else>
                    <Button as-child>
                        <Link :href="route('login')">
                            <UserCircleIcon class="size-5 me-2" />
                            Đăng nhập
                        </Link>
                    </Button>
                </template>
            </div>
        </div>

        <!-- Header Ad -->
        <AdWidget position="header" />
    </header>
</template>
