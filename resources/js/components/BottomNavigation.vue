<script setup lang="ts">
import Bars3Icon from '@/components/Icons/Bars3Icon.vue'
import CreditCardIcon from '@/components/Icons/CreditCardIcon.vue'
import HomeIcon from '@/components/Icons/HomeIcon.vue'
import UserCircleIcon from '@/components/Icons/UserCircleIcon.vue'
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet'
import { Link, router, usePage } from '@inertiajs/vue3'
import type { Component } from 'vue'
import { ref } from 'vue'
import AdWidget from './AdWidget.vue'

interface Item {
    icon: Component
    text: string
    url: string
    active: boolean
}

const items: Item[] = [
    {
        icon: HomeIcon,
        text: 'Trang chủ',
        url: route('home'),
        active: route().current() === 'home',
    },
    {
        icon: CreditCardIcon,
        text: 'Nạp tiền',
        url: route('user.deposit.index'),
        active: route().current() === 'user.deposit.index',
    },
    {
        icon: UserCircleIcon,
        text: '<PERSON><PERSON><PERSON> kho<PERSON>n',
        url: route('user.profile'),
        active: route().current() === 'user.profile',
    },
]

const isMenuOpen = ref<boolean>(false)
const expandedItems = ref<Set<number>>(new Set())

const toggleMenu = () => {
    isMenuOpen.value = !isMenuOpen.value
}

const toggleExpanded = (index: number) => {
    if (expandedItems.value.has(index)) {
        expandedItems.value.delete(index)
    } else {
        expandedItems.value.add(index)
    }
}

router.on('before', () => {
    isMenuOpen.value = false
    expandedItems.value.clear()
})

const menu = usePage().props.app.menu
</script>

<template>
    <div
        class="lg:hidden fixed bottom-0 z-10 w-full border-t bg-background shadow-xs"
    >
        <div class="grid grid-cols-4 h-full">
            <Link
                v-for="(item, index) in items"
                :key="index"
                :href="item.url"
                class="flex h-full flex-col items-center justify-between group py-2 gap-1"
            >
                <component
                    :is="item.icon"
                    class="size-5 text-muted-foreground"
                />
                <span class="text-sm text-center text-secondary-foreground">
                    {{ item.text }}
                </span>
            </Link>
            <button
                type="button"
                class="flex h-full flex-col items-center justify-between group py-2 gap-1"
                @click="toggleMenu"
            >
                <Bars3Icon class="size-5 text-muted-foreground" />
                <span class="text-sm text-center text-secondary-foreground">
                    Menu
                </span>
            </button>
        </div>
    </div>

    <Sheet v-model:open="isMenuOpen">
        <SheetContent side="left" class="w-[300px] sm:w-[400px]">
            <SheetHeader>
                <SheetTitle>
                    <Link
                        :href="route('home')"
                        class="block w-full flex items-center justify-center"
                    >
                        <img
                            v-if="$page.props.app.logo"
                            v-lazy="$page.props.app.logo"
                            :alt="$page.props.app.name"
                            class="w-auto max-h-24"
                        />
                        <span
                            v-else
                            class="text-2xl font-medium hover:text-primary transition-all"
                        >
                            {{ $page.props.app.name }}
                        </span>
                    </Link>
                </SheetTitle>
            </SheetHeader>

            <div class="mt-8 space-y-1">
                <template v-for="(item, index) in menu" :key="index">
                    <div v-if="item.children.length > 0" class="w-full">
                        <button
                            type="button"
                            @click="toggleExpanded(index)"
                            class="flex w-full items-center justify-between py-3 px-4 hover:bg-accent hover:text-accent-foreground rounded-md transition-colors font-medium"
                        >
                            <span class="text-left">{{ item.title }}</span>
                            <svg
                                class="w-4 h-4 transition-transform duration-200"
                                :class="{
                                    'rotate-180': expandedItems.has(index),
                                }"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M19 9l-7 7-7-7"
                                />
                            </svg>
                        </button>
                        <div
                            v-show="expandedItems.has(index)"
                            class="overflow-hidden transition-all duration-200"
                        >
                            <div class="space-y-1 pl-4 py-1">
                                <Link
                                    v-for="(child, childIndex) in item.children"
                                    :key="childIndex"
                                    :href="child.url"
                                    class="flex items-center py-2 px-3 rounded-md hover:bg-accent hover:text-accent-foreground transition-colors text-sm"
                                >
                                    {{ child.title }}
                                </Link>
                            </div>
                        </div>
                    </div>

                    <Link
                        v-else
                        :href="item.url"
                        class="flex items-center py-3 px-4 rounded-md hover:bg-accent hover:text-accent-foreground transition-colors font-medium"
                    >
                        {{ item.title }}
                    </Link>
                </template>
            </div>
        </SheetContent>
    </Sheet>

    <AdWidget position="mobile_bottom" />
</template>
