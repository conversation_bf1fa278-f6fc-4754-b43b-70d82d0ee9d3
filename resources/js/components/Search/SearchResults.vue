<script setup lang="ts">
import Spinner from '@/components/Spinner.vue'
import { computed } from 'vue'
import SearchResultItem, { type SearchResult } from './SearchResultItem.vue'

interface Props {
    results: SearchResult[]
    loading?: boolean
    query?: string
    activeIndex?: number
    showEmpty?: boolean
}

interface Emits {
    (e: 'result-click', result: SearchResult): void
}

const props = withDefaults(defineProps<Props>(), {
    results: () => [],
    loading: false,
    query: '',
    activeIndex: -1,
    showEmpty: true,
})

const emit = defineEmits<Emits>()

const groupedResults = computed(() => {
    const groups: Record<string, SearchResult[]> = {}

    props.results.forEach((result) => {
        const category = result.category || 'Khác'
        if (!groups[category]) {
            groups[category] = []
        }
        groups[category].push(result)
    })

    return groups
})

const hasResults = computed(() => props.results.length > 0)

const handleResultClick = (result: SearchResult) => {
    emit('result-click', result)
}
</script>

<template>
    <div class="w-full">
        <div class="min-h-[200px] max-h-96 overflow-y-auto">
            <div v-if="loading" class="flex items-center justify-center py-8">
                <Spinner class="w-6 h-6" />
                <span class="ml-2 text-sm text-muted-foreground">
                    Đang tìm kiếm...
                </span>
            </div>

            <div v-else-if="hasResults">
                <div
                    v-for="(categoryResults, category) in groupedResults"
                    :key="category"
                    class="mb-4 last:mb-0"
                >
                    <div
                        class="sticky top-0 bg-background/95 backdrop-blur-sm border-b px-3 py-2"
                    >
                        <h4
                            class="text-xs font-medium text-muted-foreground uppercase tracking-wide"
                        >
                            {{ category }} ({{ categoryResults.length }})
                        </h4>
                    </div>

                    <div class="divide-y divide-border">
                        <SearchResultItem
                            v-for="(result, index) in categoryResults"
                            :key="result.id"
                            :result="result"
                            :search-query="query"
                            :is-active="
                                activeIndex === props.results.indexOf(result)
                            "
                            @click="handleResultClick"
                        />
                    </div>
                </div>
            </div>

            <div
                v-else-if="showEmpty && query.trim()"
                class="flex flex-col items-center justify-center py-8 text-center"
            >
                <div
                    class="w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4"
                >
                    <span class="text-2xl">🔍</span>
                </div>
                <h3 class="font-medium text-foreground mb-1">
                    Không tìm thấy kết quả
                </h3>
                <p class="text-xs text-muted-foreground max-w-sm">
                    Không tìm thấy kết quả nào cho "<strong>{{ query }}</strong
                    >". Hãy thử với từ khóa khác.
                </p>
            </div>

            <div
                v-else-if="showEmpty"
                class="flex flex-col items-center justify-center py-8 text-center"
            >
                <div
                    class="w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4"
                >
                    <span class="text-2xl">💡</span>
                </div>
                <h3 class="font-medium text-foreground mb-1">
                    Bắt đầu tìm kiếm
                </h3>
                <p class="text-xs text-muted-foreground max-w-sm">
                    Nhập từ khóa để tìm kiếm tài khoản game, bài viết, danh mục
                    và nhiều hơn nữa.
                </p>
            </div>
        </div>
    </div>
</template>
