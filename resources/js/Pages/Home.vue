<script setup lang="ts">
import AccountCategoryList from '@/components/AccountCategory/AccountCategoryList.vue'
import AdWidget from '@/components/AdWidget.vue'
import FlashSaleList from '@/components/FlashSale/FlashSaleList.vue'
import SectionHeading from '@/components/SectionHeading.vue'
import type { Depositor } from '@/components/TopDepositor/TopDepositorItem.vue'
import TopDepositorList from '@/components/TopDepositor/TopDepositorList.vue'
import {
    Carousel,
    CarouselContent,
    CarouselItem,
} from '@/components/ui/carousel'
import WheelList from '@/components/Wheel/WheelList.vue'
import type { AccountCategory, FlashSale, Game, Wheel } from '@/types'
import { Link } from '@inertiajs/vue3'
import { computed } from 'vue'

const props = defineProps<{
    categories: AccountCategory[]
    wheels: Wheel[]
    games: Game[]
    bannerSliders: {
        label: string | null
        image: string
        url: string | null
        open_in_new_tab: boolean
    }[]
    topDepositors: Depositor[]
    flashSale?: FlashSale
}>()

const groupedCategories = computed(() => {
    const parentCategories = props.categories.filter((cat) => !cat.parent_id)
    const childCategories = props.categories.filter((cat) => cat.parent_id)

    return parentCategories
        .map((parent) => ({
            parent,
            children: childCategories.filter(
                (child) => child.parent_id == parent.id,
            ),
        }))
        .filter((group) => group.children.length > 0)
})

const parentCategoriesWithoutChildren = computed(() => {
    const parentCategories = props.categories.filter((cat) => !cat.parent_id)
    const childCategories = props.categories.filter((cat) => cat.parent_id)

    return parentCategories.filter((parent) => {
        const hasChildren = childCategories.some(
            (child) => child.parent_id == parent.id,
        )
        return !hasChildren
    })
})
</script>

<template>
    <div class="container py-4">
        <section
            class="mb-8"
            v-if="bannerSliders.length || topDepositors.length"
        >
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-2 sm:gap-4">
                <div
                    v-if="bannerSliders.length"
                    :class="{
                        'col-span-12 lg:col-span-8 xl:col-span-9':
                            topDepositors.length,
                        'col-span-12': !topDepositors.length,
                    }"
                >
                    <div
                        class="h-full overflow-hidden max-w-full rounded-lg ring-1 ring-gray-950/10 dark:ring-white/10"
                    >
                        <Carousel>
                            <CarouselContent>
                                <CarouselItem
                                    v-for="(
                                        bannerSlider, index
                                    ) in bannerSliders"
                                    :key="index"
                                >
                                    <component
                                        :is="bannerSlider.url ? 'a' : 'div'"
                                        :href="bannerSlider.url || null"
                                        :target="
                                            bannerSlider.open_in_new_tab
                                                ? '_blank'
                                                : '_self'
                                        "
                                    >
                                        <img
                                            v-lazy="bannerSlider.image"
                                            :alt="
                                                bannerSlider.label ||
                                                $page.props.app.name
                                            "
                                            class="h-full w-full object-cover"
                                        />
                                    </component>
                                </CarouselItem>
                            </CarouselContent>
                        </Carousel>
                    </div>
                </div>
                <div
                    class="col-span-12 lg:col-span-4 xl:col-span-3"
                    v-if="topDepositors.length"
                >
                    <TopDepositorList :topDepositors="topDepositors" />
                </div>
            </div>
        </section>

        <section
            class="mb-8"
            v-if="
                flashSale && flashSale.accounts && flashSale.accounts.length > 0
            "
        >
            <FlashSaleList :flash-sale="flashSale" />
        </section>

        <section class="mb-8" v-if="wheels.length">
            <SectionHeading heading="Vòng quay may mắn" />

            <WheelList :wheels="wheels" />
        </section>

        <!-- Home Banner Ad -->
        <AdWidget position="home_banner" />

        <!-- <section class="mb-8" v-if="games.length">
            <div class="flex items-center justify-between mb-6">
                <h2
                    class="text-xl sm:text-2xl font-bold text-center text-primary"
                >
                    Danh sách game
                </h2>
                <Link
                    :href="route('games.index')"
                    class="text-primary text-sm font-medium"
                >
                    Xem tất cả
                    <svg
                        class="inline w-4 h-4 ml-1"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 5l7 7-7 7"
                        />
                    </svg>
                </Link>
            </div>

            <div class="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                <div
                    v-for="game in games.slice(0, 8)"
                    :key="game.id"
                    class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200 relative group border border-gray-200 dark:border-gray-700"
                >
                    <div class="aspect-square bg-gray-200 dark:bg-gray-700">
                        <img
                            v-if="game.image"
                            v-lazy="game.image"
                            :alt="game.name"
                            class="w-full h-full object-cover"
                        />
                        <div
                            v-else
                            class="w-full h-full flex items-center justify-center text-gray-500 dark:text-gray-400"
                        >
                            <svg
                                class="w-8 h-8"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                            >
                                <path
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                        </div>
                    </div>

                    <div class="p-2">
                        <h3
                            class="font-medium text-gray-900 dark:text-white truncate text-center"
                        >
                            <Link
                                :href="route('games.show', game.slug)"
                                class="hover:text-primary transition-colors"
                            >
                                {{ game.name }}
                            </Link>
                        </h3>
                        <p
                            class="text-xs text-gray-500 dark:text-gray-400 text-center mt-1"
                        >
                            {{
                                game.categories_count ||
                                game.categories?.length ||
                                0
                            }}
                            danh mục chính
                        </p>
                    </div>

                    <Link
                        :href="route('games.show', game.slug)"
                        class="absolute inset-0"
                        :aria-label="`Xem ${game.name}`"
                    />
                </div>
            </div>
        </section> -->

        <section
            v-for="group in groupedCategories"
            :key="group.parent.id"
            class="mb-8"
        >
            <SectionHeading :heading="group.parent.name" />

            <AccountCategoryList :categories="group.children" />

            <div class="mt-4 text-center">
                <Link
                    :href="route('categories.show', group.parent.slug)"
                    class="inline-flex items-center text-sm font-medium text-primary hover:text-primary/80 transition-colors"
                >
                    Xem tất cả {{ group.parent.name }}
                    <svg
                        class="inline w-4 h-4 ml-1"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 5l7 7-7 7"
                        />
                    </svg>
                </Link>
            </div>
        </section>

        <section v-if="parentCategoriesWithoutChildren.length > 0" class="mb-8">
            <SectionHeading heading="Tài khoản khác" />

            <AccountCategoryList
                :categories="parentCategoriesWithoutChildren"
            />
        </section>
    </div>
</template>
